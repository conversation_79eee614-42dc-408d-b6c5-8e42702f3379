#!/usr/bin/env python3

import os
import re
import struct
from pathlib import Path

# First, let's check if we have the output.raw file or need to create it
# Since we only have flag_22.enc, we need to work backwards

def analyze_challenge():
    """
    Analyze the challenge structure to understand the decryption process
    """
    print("=== Mechanic CTF Challenge Analysis ===")
    
    # Check what files we have
    current_files = list(Path('.').glob('*'))
    print(f"Available files: {[f.name for f in current_files]}")
    
    # The challenge shows we have flag_22.enc as the final encrypted file
    # This means there were 22 encryption rounds (c goes from 0 to 22)
    # So we need to work backwards from flag_22.enc to flag_21.enc, flag_20.enc, etc.
    
    print("\nChallenge Logic:")
    print("1. Random number m is generated between 2^39 and 2^40")
    print("2. Binary representation of m determines encryption pattern")
    print("3. For each '1' bit: real encryption happens")
    print("4. For each '0' bit: fake keys are generated")
    print("5. All secret keys are written to output.raw")
    print("6. Final encrypted file is flag_22.enc")
    
    print(f"\nWe have flag_22.enc, which means 23 encryption rounds occurred")
    print("This means the binary representation of m had 23 '1' bits")
    
    # Since m is between 2^39 and 2^40, it has 40 or 41 bits total
    # But only 23 of them were '1' (causing encryption)
    
    return True

def analyze_encrypted_file():
    """
    Analyze the encrypted file to understand the structure
    """
    print("\n=== Analyzing Encrypted File ===")

    # Read the encrypted file
    with open('flag_22.enc', 'rb') as f:
        data = f.read()

    print(f"File size: {len(data)} bytes")

    # Look for patterns in the file
    # The file might contain references to previous files
    data_str = data.decode('latin-1')  # Use latin-1 to preserve all bytes

    # Look for flag_ references
    import re
    flag_refs = re.findall(r'flag_\d+\.enc', data_str)
    print(f"Found flag references: {flag_refs}")

    # This tells us the encryption chain
    return flag_refs

def understand_encryption_chain():
    """
    The key insight is that we need to work backwards from flag_22.enc
    But we need the secret keys from output.raw to decrypt
    """
    print("\n=== Understanding the Vulnerability ===")

    print("The vulnerability is likely that:")
    print("1. We don't have output.raw with the secret keys")
    print("2. But the encryption is deterministic in some way")
    print("3. Or there's a weakness in the key generation")

    # The hint about Schrödinger's cat suggests we need to try both states
    # This might mean we can brute force the binary pattern

    # Since m is between 2^39 and 2^40, let's check the actual range
    min_m = 2**39
    max_m = 2**40

    print(f"Range size: {max_m - min_m} = {max_m - min_m:,}")

    # Let's check what the minimum number of 1s would be
    # The minimum value 2^39 has exactly 1 bit set
    # The maximum value 2^40 has exactly 1 bit set
    # But numbers in between can have many bits set

    # Let's sample some numbers to see typical bit counts
    import random
    samples = []
    for _ in range(100):
        test_m = random.randint(min_m, max_m-1)
        binary = bin(test_m)[2:]
        ones_count = binary.count('1')
        samples.append(ones_count)

    print(f"Sample bit counts: min={min(samples)}, max={max(samples)}, avg={sum(samples)/len(samples):.1f}")

    return samples

def extract_embedded_files():
    """
    Try to extract the embedded encrypted files from flag_22.enc
    """
    print("\n=== Extracting Embedded Files ===")

    with open('flag_22.enc', 'rb') as f:
        data = f.read()

    # Look for the pattern that indicates embedded files
    # The KryptonKEM likely has a specific format

    # Let's look for the flag_21.enc reference and extract that part
    data_str = data.decode('latin-1')

    # Find where flag_21.enc is mentioned
    flag21_pos = data_str.find('flag_21.enc')
    if flag21_pos != -1:
        print(f"Found flag_21.enc at position {flag21_pos}")

        # The encrypted file likely starts after some header
        # Let's try to find the actual encrypted content

        # Look for patterns around the flag_21.enc reference
        start_context = max(0, flag21_pos - 100)
        end_context = min(len(data_str), flag21_pos + 200)
        context = data_str[start_context:end_context]

        print("Context around flag_21.enc:")
        print(repr(context))

        # Try to extract what looks like the embedded file
        # The format might be: [header][embedded_file_content]

        # Let's try to find where the actual encrypted content starts
        # It's likely after the filename reference

        potential_start = flag21_pos + len('flag_21.enc')

        # Skip any null bytes or padding
        while potential_start < len(data) and data[potential_start] in [0, ord('\x00')]:
            potential_start += 1

        print(f"Potential embedded file starts at position {potential_start}")

        # Extract everything from that point to the end as flag_21.enc
        if potential_start < len(data):
            embedded_data = data[potential_start:]

            # Write it to flag_21.enc
            with open('flag_21.enc', 'wb') as f:
                f.write(embedded_data)

            print(f"Extracted flag_21.enc ({len(embedded_data)} bytes)")
            return True

    return False

def extract_all_embedded_files():
    """
    Extract all embedded files in the chain
    """
    print("\n=== Extracting All Embedded Files ===")

    current_file = 'flag_22.enc'
    extracted_files = []

    while True:
        print(f"\nProcessing {current_file}")

        if not os.path.exists(current_file):
            print(f"File {current_file} not found")
            break

        with open(current_file, 'rb') as f:
            data = f.read()

        print(f"File size: {len(data)} bytes")

        # Look for flag_ references
        data_str = data.decode('latin-1')
        flag_refs = re.findall(r'flag_(\d+)\.enc', data_str)

        if not flag_refs:
            print("No more embedded files found")
            break

        # Get the next file number (should be current - 1)
        next_num = int(flag_refs[0])
        next_file = f'flag_{next_num}.enc'

        print(f"Found reference to {next_file}")

        # Find the position and extract
        flag_pos = data_str.find(next_file)
        if flag_pos == -1:
            print(f"Could not find {next_file} in data")
            break

        # Extract the embedded file
        potential_start = flag_pos + len(next_file)

        # Skip any null bytes or padding
        while potential_start < len(data) and data[potential_start] in [0, ord('\x00')]:
            potential_start += 1

        if potential_start >= len(data):
            print("No data after filename")
            break

        embedded_data = data[potential_start:]

        # Write the extracted file
        with open(next_file, 'wb') as f:
            f.write(embedded_data)

        print(f"Extracted {next_file} ({len(embedded_data)} bytes)")
        extracted_files.append(next_file)

        current_file = next_file

        # Safety check to avoid infinite loops
        if len(extracted_files) > 30:
            print("Too many files, stopping")
            break

    return extracted_files

def analyze_file_structure(filename):
    """
    Analyze the structure of an encrypted file to understand the format
    """
    print(f"\n=== Analyzing {filename} Structure ===")

    with open(filename, 'rb') as f:
        data = f.read()

    print(f"File size: {len(data)} bytes")

    # Look at the first few bytes
    print("First 32 bytes (hex):", data[:32].hex())
    print("First 32 bytes (ascii):", repr(data[:32]))

    # Look for patterns that might indicate structure
    # The format might be: [size][data][size][data]...

    # Check if the first bytes are a size indicator
    if data[:16] == b'0000001615000104':
        print("Detected header pattern: 0000001615000104")
        # This might be two 8-byte size fields
        size1 = int(data[:8])
        size2 = int(data[8:16])
        print(f"Potential sizes: {size1}, {size2}")

    # Look for embedded filenames
    data_str = data.decode('latin-1', errors='ignore')
    flag_matches = re.finditer(r'(\d{4})?flag_(\d+)\.enc', data_str)

    for match in flag_matches:
        pos = match.start()
        prefix = match.group(1) if match.group(1) else ""
        file_num = match.group(2)
        print(f"Found flag_{file_num}.enc at position {pos} with prefix '{prefix}'")

        # Look at context around this position
        start = max(0, pos - 20)
        end = min(len(data), pos + 50)
        context = data[start:end]
        print(f"Context: {context.hex()}")
        print(f"Context (ascii): {repr(context)}")

    return data

if __name__ == "__main__":
    analyze_challenge()
    analyze_encrypted_file()
    understand_encryption_chain()

    # Extract all embedded files
    extracted_files = extract_all_embedded_files()
    print(f"\nExtracted {len(extracted_files)} files: {extracted_files}")

    # List all files we now have
    all_files = list(Path('.').glob('flag_*.enc'))
    all_files.sort(key=lambda x: int(x.stem.split('_')[1]))
    print(f"All flag files: {[f.name for f in all_files]}")

    # Analyze the structure of the files
    for filename in ['flag_22.enc', 'flag_21.enc']:
        if os.path.exists(filename):
            analyze_file_structure(filename)
